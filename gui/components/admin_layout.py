"""
Admin layout components without sidebar.
"""
import flet as ft
from gui.config.constants import ROUTE_ADMIN_DASHBOARD, ROUTE_ADMIN_TEACHERS, ROUTE_LOGIN

def create_admin_app_bar(page: ft.Page, title: str):
    """Create admin app bar with navigation."""
    current_user = getattr(page.app_state, 'current_user', None)
    
    def logout_click(_):
        """Handle logout."""
        page.session.remove("current_user")
        page.app_state.current_user = None
        page.go(ROUTE_LOGIN)
    
    def dashboard_click(_):
        """Navigate to admin dashboard."""
        page.go(ROUTE_ADMIN_DASHBOARD)
    
    def teachers_click(_):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)
    
    def data_click(_):
        """Navigate to data overview."""
        page.go("/admin/data")
    
    # Admin navigation buttons
    nav_buttons = ft.Row([
        ft.TextButton(
            "Dashboard",
            icon=ft.Icons.DASHBOARD,
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_DASHBOARD else ft.Colors.GREY_600,
                text_style=ft.TextStyle(weight=ft.FontWeight.W_500 if page.route == ROUTE_ADMIN_DASHBOARD else ft.FontWeight.NORMAL)
            ),
            on_click=dashboard_click
        ),
        ft.TextButton(
            "Teachers",
            icon=ft.Icons.PEOPLE,
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600 if page.route == ROUTE_ADMIN_TEACHERS else ft.Colors.GREY_600,
                text_style=ft.TextStyle(weight=ft.FontWeight.W_500 if page.route == ROUTE_ADMIN_TEACHERS else ft.FontWeight.NORMAL)
            ),
            on_click=teachers_click
        ),
        ft.TextButton(
            "Data",
            icon=ft.Icons.ANALYTICS,
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600 if page.route == "/admin/data" else ft.Colors.GREY_600,
                text_style=ft.TextStyle(weight=ft.FontWeight.W_500 if page.route == "/admin/data" else ft.FontWeight.NORMAL)
            ),
            on_click=data_click
        ),
    ], spacing=10)
    
    # User info and logout
    user_section = ft.Row([
        ft.Text(
            f"Admin: {current_user.get('full_name', 'Admin') if current_user else 'Admin'}",
            size=14,
            color=ft.Colors.GREY_700
        ),
        ft.IconButton(
            icon=ft.Icons.LOGOUT,
            tooltip="Logout",
            icon_color=ft.Colors.RED_600,
            on_click=logout_click
        )
    ], spacing=5)
    
    return ft.AppBar(
        title=ft.Text(title, size=20, weight=ft.FontWeight.BOLD),
        center_title=False,
        bgcolor=ft.Colors.SURFACE,
        actions=[
            ft.Container(
                content=nav_buttons,
                padding=ft.padding.symmetric(horizontal=10)
            ),
            ft.Container(
                content=user_section,
                padding=ft.padding.symmetric(horizontal=10)
            )
        ]
    )

def create_admin_page_layout(page: ft.Page, title: str, content):
    """
    Create an admin page layout without sidebar.
    
    Args:
        page: The Flet page object
        title: The page title
        content: The page content
    
    Returns:
        ft.View: The page view
    """
    # Create admin app bar
    app_bar = create_admin_app_bar(page, title)
    
    # Create content column
    is_mobile = getattr(page, 'is_mobile', False)
    
    content_column = []
    
    if isinstance(content, list):
        content_column.extend(content)
    else:
        content_column.append(content)
    
    # Create main content area with scroll
    main_content = ft.Column(
        content_column,
        expand=True,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        alignment=ft.MainAxisAlignment.START,
        scroll=ft.ScrollMode.AUTO
    )
    
    # Create the layout
    return ft.View(
        page.route,
        [
            app_bar,
            ft.Container(
                content=main_content,
                expand=True,
                padding=ft.padding.all(20 if not is_mobile else 10)
            )
        ],
        padding=0,
        spacing=0
    )
