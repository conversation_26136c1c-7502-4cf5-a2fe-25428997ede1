"""
Admin dashboard view for managing teachers and viewing system statistics.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS
from gui.config.language import get_text

def create_admin_dashboard_view(page: ft.Page):
    """Create the admin dashboard with statistics and teacher management."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])

    # Get statistics
    stats = auth_service.get_admin_statistics()

    def manage_teachers_click(_):
        """Navigate to teacher management."""
        page.go(ROUTE_ADMIN_TEACHERS)

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                f"Welcome, {current_user.get('full_name', 'Admin')}",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Administrator Dashboard",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center,
    )

    # Statistics cards with modern design
    def create_stat_card(title: str, value: int, icon: str, color: str):
        """Create a statistics card."""
        card_width = page.width*0.85 if is_mobile else 280

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(icon, size=28, color=ft.Colors.WHITE),
                        bgcolor=color,
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.all(12)
                    ),
                    ft.Column([
                        ft.Text(
                            title,
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_200
                        ),
                        ft.Text(
                            str(value),
                            size=32,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ON_SURFACE
                        )
                    ], spacing=4, alignment=ft.MainAxisAlignment.CENTER, expand=True)
                ], alignment=ft.MainAxisAlignment.START, spacing=16),
            ], spacing=0),
            width=card_width,
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            margin=ft.margin.all(8)
        )

    # Create statistics cards
    stats_cards = []
    stats_cards.append(create_stat_card("Active Teachers", stats.get('active_teachers', 0), ft.Icons.PERSON, ft.Colors.BLUE_600))
    stats_cards.append(create_stat_card("Total Classes", stats.get('total_classes', 0), ft.Icons.CLASS_, ft.Colors.GREEN_600))
    stats_cards.append(create_stat_card("Total Students", stats.get('total_students', 0), ft.Icons.PEOPLE, ft.Colors.ORANGE_600))
    stats_cards.append(create_stat_card("Total Subjects", stats.get('total_subjects', 0), ft.Icons.BOOK, ft.Colors.PURPLE_600))
    stats_cards.append(create_stat_card("Total Quizzes", stats.get('total_quizzes', 0), ft.Icons.QUIZ, ft.Colors.RED_600))
    stats_cards.append(create_stat_card("Recent Attendance", stats.get('recent_attendance', 0), ft.Icons.FACT_CHECK, ft.Colors.TEAL_600))

    # Enhanced layout for cards with better spacing
    if is_mobile:
        cards_layout = ft.Column(
            stats_cards,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=16,
            expand=True
        )
    else:
        cards_layout = ft.Row(
            stats_cards,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=20,
            wrap=True
        )

    # Action buttons with modern styling
    manage_teachers_btn = ft.ElevatedButton(
        text="Manage Teachers",
        icon=ft.Icons.PERSON_ADD,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=32, vertical=16),
            text_style=ft.TextStyle(size=18, weight=ft.FontWeight.W_500),
            shape=ft.RoundedRectangleBorder(radius=12),
            shadow_color=ft.Colors.BLUE_200,
            elevation=2
        ),
        on_click=manage_teachers_click
    )

    view_data_btn = ft.ElevatedButton(
        text="View All Data",
        icon=ft.Icons.ANALYTICS,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.GREEN_600,
            color=ft.Colors.WHITE,
            padding=ft.padding.symmetric(horizontal=32, vertical=16),
            text_style=ft.TextStyle(size=18, weight=ft.FontWeight.W_500),
            shape=ft.RoundedRectangleBorder(radius=12),
            shadow_color=ft.Colors.GREEN_200,
            elevation=2
        ),
        on_click=lambda _: page.go("/admin/data")
    )

    # Quick actions section
    actions_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "Quick Actions",
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([manage_teachers_btn, view_data_btn], spacing=12),
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 500,
        padding=ft.padding.all(20),
        margin=ft.margin.only(top=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    # Create enhanced dashboard content
    content = [
        welcome_section,
        cards_layout,
        ft.Container(
            content=actions_section,
            alignment=ft.alignment.center,
        )
    ]

    return create_admin_page_layout(
        page,
        "Admin Dashboard",
        content
    )
