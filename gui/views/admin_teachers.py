"""
Teacher management view for admin.
"""
import flet as ft
from gui.components.layout import create_page_layout
from gui.components.dialogs import create_confirmation_dialog, create_form_dialog, show_dialog, close_dialog
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD, ICON_ADD
from gui.config.language import get_text

def create_admin_teachers_view(page: ft.Page):
    """Create the teacher management view."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/teachers", controls=[])

    # Get initial teachers data
    teachers = auth_service.get_all_teachers()

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "Teacher Management",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                f"{len(teachers)} teachers in the system",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center,
    )

    # Create a modern column for teacher cards
    teachers_container = ft.Column(
        spacing=16,
        width=page.width*0.9 if is_mobile else 600,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
    )

    # Store reference to containers for dynamic updates
    page._teachers_container = teachers_container

    def refresh_teachers():
        """Refresh the teachers list."""
        teachers_container.controls.clear()
        fresh_teachers = auth_service.get_all_teachers()

        # Update welcome section with teacher count
        welcome_section.content.controls[1].value = f"{len(fresh_teachers)} teachers in the system"

        # Update no teachers message visibility
        no_teachers_message.visible = len(fresh_teachers) == 0

        if len(fresh_teachers) == 0:
            page.update()
            return

        # Create teacher cards
        for teacher in fresh_teachers:
            teacher_card = create_teacher_card(teacher)
            teachers_container.controls.append(teacher_card)

        page.update()

    def create_teacher_card(teacher: dict):
        """Create a teacher card with modern styling."""
        status_color = ft.Colors.GREEN_600 if teacher['is_active'] else ft.Colors.RED_600
        status_text = "Active" if teacher['is_active'] else "Inactive"

        def create_toggle_handler(teacher_data):
            def toggle_status(_):
                new_status = not teacher_data['is_active']
                if auth_service.update_teacher_status(teacher_data['id'], new_status):
                    refresh_teachers()
                    page.app_state.show_success(f"Teacher status updated to {'Active' if new_status else 'Inactive'}")
            return toggle_status

        def create_delete_handler(teacher_data):
            def delete_teacher(_):
                def confirm_delete():
                    if auth_service.delete_teacher(teacher_data['id']):
                        refresh_teachers()
                        page.app_state.show_success("Teacher deleted successfully")

                dialog = create_confirmation_dialog(
                    page,
                    "Confirm Delete",
                    f"Are you sure you want to delete teacher '{teacher_data['full_name']}'?",
                    confirm_delete,
                    confirm_text="Delete",
                    is_destructive=True
                )
                show_dialog(page, dialog)
            return delete_teacher

        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(teacher['full_name'], size=16, weight=ft.FontWeight.W_600),
                    ft.Text(f"Username: {teacher['username']}", size=14, color=ft.Colors.GREY_600),
                    ft.Text(f"Email: {teacher['email'] or 'Not provided'}", size=14, color=ft.Colors.GREY_600),
                    ft.Text(f"Created: {teacher['created_at'][:10]}", size=12, color=ft.Colors.GREY_500),
                ], spacing=2, expand=True),
                ft.Column([
                    ft.Container(
                        content=ft.Text(status_text, color=ft.Colors.WHITE, size=12, weight=ft.FontWeight.W_500),
                        bgcolor=status_color,
                        padding=ft.padding.symmetric(horizontal=12, vertical=4),
                        border_radius=12,
                    ),
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.TOGGLE_ON if teacher['is_active'] else ft.Icons.TOGGLE_OFF,
                            icon_color=status_color,
                            tooltip="Toggle Status",
                            on_click=create_toggle_handler(teacher)
                        ),
                        ft.IconButton(
                            icon=ft.Icons.DELETE,
                            icon_color=ft.Colors.RED_600,
                            tooltip="Delete Teacher",
                            on_click=create_delete_handler(teacher)
                        ),
                    ], spacing=0)
                ], horizontal_alignment=ft.CrossAxisAlignment.END, spacing=5)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
            margin=ft.margin.all(8)
        )

    def show_add_teacher_dialog(_):
        """Show add teacher dialog."""
        username_field = ft.TextField(
            label="Username",
            hint_text="Enter username",
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        )
        password_field = ft.TextField(
            label="Password",
            hint_text="Enter password",
            password=True,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        )
        full_name_field = ft.TextField(
            label="Full Name",
            hint_text="Enter full name",
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        )
        email_field = ft.TextField(
            label="Email (Optional)",
            hint_text="Enter email (optional)",
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        )
        phone_field = ft.TextField(
            label="Phone (Optional)",
            hint_text="Enter phone (optional)",
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        )

        def add_teacher():
            if not all([username_field.value, password_field.value, full_name_field.value]):
                username_field.error_text = "Username is required" if not username_field.value else ""
                password_field.error_text = "Password is required" if not password_field.value else ""
                full_name_field.error_text = "Full name is required" if not full_name_field.value else ""
                page.update()
                return

            success = auth_service.create_teacher(
                username=username_field.value.strip(),
                password=password_field.value.strip(),
                full_name=full_name_field.value.strip(),
                email=email_field.value.strip() or None,
                phone=phone_field.value.strip() or None
            )

            if success:
                refresh_teachers()
                page.app_state.show_success("Teacher created successfully")
            else:
                page.app_state.show_error("Failed to create teacher. Username might already exist.")

        form_controls = [
            username_field,
            password_field,
            full_name_field,
            email_field,
            phone_field,
        ]

        dialog = create_form_dialog(
            page,
            "Add New Teacher",
            form_controls,
            add_teacher,
            submit_text="Add Teacher"
        )
        show_dialog(page, dialog)

    # Add teacher button
    add_teacher_button = ft.ElevatedButton(
        "Add Teacher",
        icon=ICON_ADD,
        tooltip="Add new teacher",
        on_click=show_add_teacher_dialog,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=12),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            shadow_color=ft.Colors.BLUE_200,
            elevation=2
        ),
    )

    # Modern add teacher form
    add_teacher_form = ft.Container(
        content=ft.Column([
            ft.Text(
                "Add New Teacher",
                size=18,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            add_teacher_button,
        ], spacing=12),
        width=page.width*0.9 if is_mobile else 500,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    # No teachers message
    no_teachers_message = ft.Container(
        content=ft.Column([
            ft.Icon(
                ft.Icons.PERSON_ADD,
                size=48,
                color=ft.Colors.GREY_400
            ),
            ft.Text(
                "No teachers yet",
                size=18,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
                weight=ft.FontWeight.W_500
            ),
            ft.Text(
                "Add your first teacher to get started",
                size=14,
                color=ft.Colors.GREY_500,
                text_align=ft.TextAlign.CENTER
            )
        ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        alignment=ft.alignment.center,
        padding=ft.padding.all(40),
        visible=len(teachers) == 0,
        bgcolor=ft.Colors.BLUE_GREY_200,
        border_radius=ft.border_radius.all(16),
        margin=ft.margin.only(bottom=20)
    )

    # Create teacher cards for each existing teacher (initial load)
    for teacher in teachers:
        teacher_card = create_teacher_card(teacher)
        teachers_container.controls.append(teacher_card)

    # Store the refresh function for use in dialogs
    page._refresh_teachers = refresh_teachers

    # Create enhanced content
    content = [
        welcome_section,
        ft.Container(
            content=add_teacher_form,
            alignment=ft.alignment.center,
        ),
        no_teachers_message,
        ft.Container(
            content=teachers_container,
            alignment=ft.alignment.center,
        )
    ]

    return create_page_layout(
        page,
        "",  # Empty title since we have the welcome section
        content
    )
