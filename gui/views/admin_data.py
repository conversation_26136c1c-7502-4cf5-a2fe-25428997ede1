"""
Admin data overview view showing all system data.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD
from gui.config.language import get_text

def create_admin_data_view(page: ft.Page):
    """Create the admin data overview view."""
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/data", controls=[])

    def get_all_data():
        """Get all data from the database."""
        try:
            with sqlite3.connect(Config.get_db_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                data = {}

                # Get classes
                cursor.execute("SELECT * FROM classes ORDER BY name")
                data['classes'] = [dict(row) for row in cursor.fetchall()]

                # Get students
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                data['students'] = [dict(row) for row in cursor.fetchall()]

                # Get subjects
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                data['subjects'] = [dict(row) for row in cursor.fetchall()]

                # Get quizzes
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                data['quizzes'] = [dict(row) for row in cursor.fetchall()]

                # Get attendance records
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 100
                """)
                data['attendance'] = [dict(row) for row in cursor.fetchall()]

                return data

        except Exception as e:
            print(f"❌ Failed to get data: {e}")
            return {}

    # Get all data
    all_data = get_all_data()

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "System Data Overview",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Complete view of all data in the system",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center,
    )

    def create_data_table(title: str, data: list, columns: list):
        """Create a data table for a specific data type."""
        if not data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.INBOX,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        f"No {title.lower()} found",
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER,
                        weight=ft.FontWeight.W_500
                    )
                ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                padding=ft.padding.all(40),
                bgcolor=ft.Colors.BLUE_GREY_200,
                border_radius=ft.border_radius.all(16),
                margin=ft.margin.only(bottom=20)
            )

        # Create table rows
        rows = []
        for item in data[:50]:  # Limit to 50 items for performance
            cells = []
            for col in columns:
                value = str(item.get(col, ''))
                if len(value) > 30:
                    value = value[:27] + "..."
                cells.append(ft.DataCell(ft.Text(value, size=12)))
            rows.append(ft.DataRow(cells=cells))

        # Create table columns
        table_columns = [
            ft.DataColumn(ft.Text(col.replace('_', ' ').title(), weight=ft.FontWeight.BOLD))
            for col in columns
        ]

        return ft.Container(
            content=ft.Column([
                ft.Text(
                    f"{title} ({len(data)} total)",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Container(height=10),
                ft.Container(
                    content=ft.DataTable(
                        columns=table_columns,
                        rows=rows,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=10,
                        vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        heading_row_color=ft.Colors.BLUE_50,
                        heading_row_height=50,
                    ),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=10,
                    padding=ft.padding.all(10),
                    shadow=ft.BoxShadow(
                        spread_radius=1,
                        blur_radius=5,
                        color=ft.Colors.GREY_200,
                        offset=ft.Offset(0, 2)
                    ),
                )
            ], spacing=0),
            width=page.width*0.9 if is_mobile else None,
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=20),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
        )

    # Create data sections
    data_sections = []
    data_sections.append(create_data_table("Classes", all_data.get('classes', []),
                         ['name', 'description', 'created_at']))
    data_sections.append(create_data_table("Students", all_data.get('students', []),
                         ['name', 'class_name', 'created_at']))
    data_sections.append(create_data_table("Subjects", all_data.get('subjects', []),
                         ['name', 'class_name', 'description', 'created_at']))
    data_sections.append(create_data_table("Quizzes", all_data.get('quizzes', []),
                         ['title', 'class_name', 'subject_name', 'created_at']))
    data_sections.append(create_data_table("Recent Attendance", all_data.get('attendance', []),
                         ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time']))

    # Create enhanced content
    content = [welcome_section]
    content.extend([
        ft.Container(
            content=section,
            alignment=ft.alignment.center,
        ) for section in data_sections
    ])

    return create_admin_page_layout(
        page,
        "System Data Overview",
        content
    )
